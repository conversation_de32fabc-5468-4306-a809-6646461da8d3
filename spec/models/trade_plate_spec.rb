require "rails_helper"

RSpec.describe TradePlate, type: :model do
  describe "associations" do
    it { should belong_to(:dealership) }
    it { should have_many(:drives).dependent(:nullify) }
  end

  describe "validations" do
    it { should validate_presence_of(:number) }

    describe "number uniqueness" do
      it "validates uniqueness of number scoped to dealership" do
        dealership = create(:dealership)
        create(:trade_plate, number: "TP001", dealership: dealership)

        # Same number, same dealership should be invalid
        duplicate = build(:trade_plate, number: "TP001", dealership: dealership)
        expect(duplicate).not_to be_valid
        expect(duplicate.errors[:number]).to include("has already been taken")

        # Same number, different dealership should be valid
        other_dealership = create(:dealership)
        valid_duplicate = build(:trade_plate, number: "TP001", dealership: other_dealership)
        expect(valid_duplicate).to be_valid
      end
    end
  end

  describe "enums" do
    it { should define_enum_for(:status).with_values(active: 0, inactive: 1).with_default(:active) }
  end

  describe "concerns" do
    it "includes HasUuid" do
      expect(TradePlate.included_modules).to include(HasUuid)
    end
  end

  describe "factory" do
    it "has a valid factory" do
      expect(build(:trade_plate)).to be_valid
    end

    it "creates with inactive trait" do
      trade_plate = create(:trade_plate, :inactive)
      expect(trade_plate.status).to eq("inactive")
    end
  end

  describe "uuid generation" do
    it "generates a uuid on creation" do
      trade_plate = create(:trade_plate)
      expect(trade_plate.uuid).to be_present
      expect(trade_plate.uuid).to match(/\A[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}\z/)
    end

    it "does not change uuid on update" do
      trade_plate = create(:trade_plate)
      original_uuid = trade_plate.uuid
      trade_plate.update!(number: "UPDATED123")
      expect(trade_plate.reload.uuid).to eq(original_uuid)
    end
  end

  describe "dependent associations" do
    let(:trade_plate) { create(:trade_plate) }
    let!(:drive1) { create(:drive, trade_plate: trade_plate, dealership: trade_plate.dealership) }
    let!(:drive2) { create(:drive, trade_plate: trade_plate, dealership: trade_plate.dealership) }

    it "nullifies associated drives when destroyed" do
      expect { trade_plate.destroy }
        .to change { drive1.reload.trade_plate_id }.from(trade_plate.id).to(nil)
        .and change { drive2.reload.trade_plate_id }.from(trade_plate.id).to(nil)
    end

    it "does not destroy drives when trade plate is destroyed" do
      expect { trade_plate.destroy }.not_to change { Drive.count }
    end
  end

  describe "status enum behavior" do
    let(:trade_plate) { create(:trade_plate) }

    it "defaults to active status" do
      expect(trade_plate.status).to eq("active")
      expect(trade_plate).to be_active
    end

    it "can be set to inactive" do
      trade_plate.update!(status: :inactive)
      expect(trade_plate.status).to eq("inactive")
      expect(trade_plate).to be_inactive
    end

    it "provides status query methods" do
      expect(trade_plate).to respond_to(:active?)
      expect(trade_plate).to respond_to(:inactive?)
    end

    it "provides status scopes" do
      active_trade_plate = create(:trade_plate, status: :active)
      inactive_trade_plate = create(:trade_plate, status: :inactive)

      expect(TradePlate.active).to include(active_trade_plate)
      expect(TradePlate.active).not_to include(inactive_trade_plate)
      expect(TradePlate.inactive).to include(inactive_trade_plate)
      expect(TradePlate.inactive).not_to include(active_trade_plate)
    end
  end
end
