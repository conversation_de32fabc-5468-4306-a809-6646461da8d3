# frozen_string_literal: true

require 'swagger_helper'

RSpec.describe 'Bookings API', type: :request do
  path '/api/v1/bookings' do
    post 'Creates a booking' do
      tags 'Bookings'
      description 'Creates a new booking for a test drive'
      consumes 'application/json'
      produces 'application/json'
      security [ Bearer: [] ]

      parameter name: 'Authorization', in: :header, type: :string, required: true,
                description: 'JWT token in the format: Bearer <token>'

      parameter name: 'Device-ID', in: :header, type: :string, required: true,
                description: 'Device ID'

      parameter name: :booking, in: :body, schema: {
        type: :object,
        properties: {
          dealership_uuid: { type: :string },
          vehicle_uuid: { type: :string },
          drive_type: { type: :string, enum: [ 'test_drive_booking', 'loan_booking' ] },
          expected_pickup_date_time: { type: :string, format: 'date-time' },
          expected_return_date_time: { type: :string, format: 'date-time' },
          sales_person_uuid: { type: :string },
          customer_uuid: { type: :string },
          notes: { type: :string }
        },
        required: [
          :dealership_uuid,
          :vehicle_uuid,
          :drive_type,
          :expected_pickup_date_time,
          :expected_return_date_time,
          :sales_person_uuid,
          :customer_uuid
        ]
      }

      let(:dealership) { create(:dealership) }
      let(:vehicle) { create(:vehicle, dealership: dealership) }
      let(:customer) { create(:customer, dealership: dealership) }
      let(:driver_license) { create(:driver_license, holder: customer) }
      let(:sales_person) { create(:user) }
      let(:current_user) { create(:user) }
      let!(:dealership_sales_person) { create(:user_dealership, user: sales_person, dealership: dealership) }
      let!(:dealership_manager) { create(:user_dealership, user: current_user, dealership: dealership, role: :dealership_admin) }
      let(:device_registration) { create(:device_registration, user: current_user) }
      let(:expected_pickup_time) { '2025-06-01 10:00:00' }
      let(:expected_return_time) { '2025-06-01 12:00:00' }
      let(:valid_token) { Auth::TokenService.new(device_registration).generate_tokens[:access_token] }
      let(:Authorization) { "Bearer #{valid_token}" }
      let(:"Device-ID") { device_registration.device_id }

      response '201', 'booking created with existing customer' do
        let(:booking) do
          {
            dealership_uuid: dealership.uuid,
            vehicle_uuid: vehicle.uuid,
            drive_type: "test_drive_booking",
            expected_pickup_date_time: expected_pickup_time,
            expected_return_date_time: expected_return_time,
            sales_person_uuid: sales_person.uuid,
            customer_uuid: customer.uuid,
            notes: 'Test drive booking'
          }
        end

        before do
          customer.driver_license = driver_license
        end

        run_test! do |response|
          data = JSON.parse(response.body, symbolize_names: true)
          booking_data = data[:booking]

          # Check drive attributes
          expect(booking_data[:uuid]).to be_present
          expect(booking_data[:drive_type]).to eq('test_drive_booking')
          expect(booking_data[:expected_pickup_date_time]).to eq(DateTime.parse(expected_pickup_time).iso8601)
          expect(booking_data[:expected_return_date_time]).to eq(DateTime.parse(expected_return_time).iso8601)
          expect(booking_data[:notes]).to eq('Test drive booking')

          # Check nested objects
          expect(booking_data[:vehicle][:uuid]).to eq(vehicle.uuid)
          expect(booking_data[:vehicle][:make]).to eq(vehicle.make)
          expect(booking_data[:vehicle][:model]).to eq(vehicle.model)

          expect(booking_data[:customer][:uuid]).to eq(customer.uuid)
          expect(booking_data[:customer][:first_name]).to eq(customer.first_name)
          expect(booking_data[:customer][:last_name]).to eq(customer.last_name)

          expect(booking_data[:dealership][:uuid]).to eq(dealership.uuid)
          expect(booking_data[:dealership][:name]).to eq(dealership.name)

          expect(booking_data[:sales_person][:uuid]).to eq(sales_person.uuid)
          expect(booking_data[:sales_person][:first_name]).to eq(sales_person.first_name)
          expect(booking_data[:sales_person][:last_name]).to eq(sales_person.last_name)
        end
      end

      response '201', 'booking created with new customer' do
        let(:new_customer_info) do
          {
            first_name: 'John',
            last_name: 'Doe',
            email: '<EMAIL>',
            phone_number: '+***********',
            address_line1: '123 Main St',
            city: 'Sydney',
            state: 'NSW',
            postcode: '2000',
            driver_license: {
              licence_number: 'DL12345',
              expiry_date: '2025-12-31',
              issuing_state: 'NSW',
              issuing_country: 'Australia',
              full_name: 'John Doe',
              date_of_birth: '1990-01-01'
            }
          }
        end

        let(:booking) do
          {
            dealership_uuid: dealership.uuid,
            vehicle_uuid: vehicle.uuid,
            drive_type: "test_drive_booking",
            expected_pickup_date_time: expected_pickup_time,
            expected_return_date_time: expected_return_time,
            sales_person_uuid: sales_person.uuid,
            customer_info: new_customer_info,
            notes: 'Test drive booking with new customer'
          }
        end

        run_test! do |response|
          data = JSON.parse(response.body, symbolize_names: true)
          booking_data = data[:booking]

          # Check drive attributes
          expect(booking_data[:uuid]).to be_present
          expect(booking_data[:drive_type]).to eq('test_drive_booking')
          expect(booking_data[:notes]).to eq('Test drive booking with new customer')

          # Check customer was created
          customer_data = booking_data[:customer]
          expect(customer_data[:uuid]).to be_present
          expect(customer_data[:first_name]).to eq('John')
          expect(customer_data[:last_name]).to eq('Doe')
          expect(customer_data[:email]).to eq('<EMAIL>')

          # Verify customer was persisted
          new_customer = Customer.find_by(uuid: customer_data[:uuid])
          expect(new_customer).to be_present
          expect(new_customer.email).to eq('<EMAIL>')
          expect(new_customer.dealership_id).to eq(dealership.id)

          # verify driver license was created
          driver_license = new_customer.driver_license
          expect(driver_license).to be_present
          expect(driver_license.licence_number).to eq('DL12345')
          expect(driver_license.expiry_date).to eq(Date.parse('2025-12-31'))
          expect(driver_license.issuing_state).to eq('NSW')
          expect(driver_license.issuing_country).to eq('Australia')
          expect(driver_license.full_name).to eq('John Doe')
          expect(driver_license.date_of_birth).to eq(Date.parse('1990-01-01'))
        end
      end

      response '422', 'invalid drive type' do
        let(:booking) do
          {
            dealership_uuid: dealership.uuid,
            vehicle_uuid: vehicle.uuid,
            drive_type: "test_drive",  # Using test_drive instead of test_drive_booking
            expected_pickup_date_time: expected_pickup_time,
            expected_return_date_time: expected_return_time,
            sales_person_uuid: sales_person.uuid,
            customer_uuid: customer.uuid,
            notes: 'Test drive booking'
          }
        end

        run_test! do |response|
          expect(response.body).to include('Invalid drive_type')
        end
      end

      response '201', 'booking created with test_drive_booking type' do
        let(:booking) do
          {
            dealership_uuid: dealership.uuid,
            vehicle_uuid: vehicle.uuid,
            drive_type: "test_drive_booking",
            expected_pickup_date_time: expected_pickup_time,
            expected_return_date_time: expected_return_time,
            sales_person_uuid: sales_person.uuid,
            customer_uuid: customer.uuid,
            notes: 'Test drive booking'
          }
        end

        run_test!
      end

      response '201', 'booking created with loan_booking type' do
        let(:booking) do
          {
            dealership_uuid: dealership.uuid,
            vehicle_uuid: vehicle.uuid,
            drive_type: "loan_booking",
            expected_pickup_date_time: expected_pickup_time,
            expected_return_date_time: expected_return_time,
            sales_person_uuid: sales_person.uuid,
            customer_uuid: customer.uuid,
            notes: 'Loan booking'
          }
        end

        run_test!
      end

      response '401', 'unauthorized' do
        context 'when Device ID is nil' do
          let(:"Device-ID") { nil }
          let(:booking) { {} }
          run_test! do |response|
            json = response.parsed_body
            expect(json.dig("status", "message")).to eq("Invalid device")
          end
        end

        context 'when invalid token is provided' do
          let(:Authorization) { 'Bearer invalid_token' }
          let(:booking) { {} }
          run_test! do |response|
            json = response.parsed_body
            expect(json.dig("status", "message")).to eq("Invalid token")
          end
        end

        context 'when token is expired' do
          let(:Authorization) { "Bearer #{generate_expired_token(current_user, device_registration.device_id)}" }
          let(:booking) { {} }
          run_test! do |response|
            json = response.parsed_body
            expect(json.dig("status", "message")).to eq("Access token expired, please refresh")
          end
        end
      end

      response '404', 'customer or vehicle not found' do
        context 'when customer is not found' do
          let(:booking) do
            {
              dealership_uuid: dealership.uuid,
              vehicle_uuid: vehicle.uuid,
              drive_type: "test_drive_booking",
              expected_pickup_date_time: expected_pickup_time,
              expected_return_date_time: expected_return_time,
              sales_person_uuid: sales_person.uuid,
              customer_uuid: 'non-existent',
              notes: 'Test drive booking'
            }
          end

          run_test! do |response|
            data = JSON.parse(response.body, symbolize_names: true)
            expect(data[:status][:message]).to include("Couldn't find Customer")
          end
        end

        context 'when vehicle does not belong to dealership' do
          let(:other_dealership) { create(:dealership) }
          let(:other_vehicle) { create(:vehicle, dealership: other_dealership) }
          let(:booking) do
            {
              dealership_uuid: dealership.uuid,
              vehicle_uuid: other_vehicle.uuid,
              drive_type: "test_drive_booking",
              expected_pickup_date_time: expected_pickup_time,
              expected_return_date_time: expected_return_time,
              sales_person_uuid: sales_person.uuid,
              customer_uuid: customer.uuid,
              notes: 'Test drive booking'
            }
          end

          run_test!
        end
      end

      response '422', 'invalid request' do
        let(:expected_pickup_time) { '2025-06-01 12:00:00' } # end time equal or before start time
        let(:booking) do
          {
            dealership_uuid: dealership.uuid,
            vehicle_uuid: vehicle.uuid,
            drive_type: 'test_drive_booking',
            expected_pickup_date_time: expected_pickup_time,
            expected_return_date_time: expected_return_time,
            sales_person_uuid: sales_person.uuid,
            customer_uuid: customer.uuid,
            notes: 'Test drive booking'
          }
        end

        run_test! do |response|
          data = JSON.parse(response.body, symbolize_names: true)
          expect(data[:status][:message]).to include("must be after pickup time")
        end
      end
    end
  end
end
