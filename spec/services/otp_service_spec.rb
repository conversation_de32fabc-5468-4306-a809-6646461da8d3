require 'rails_helper'

RSpec.describe OtpService do
  let(:user) { create(:user, otp_resend_count: 0) }
  let(:service) { described_class.new(user) }
  let(:action) { I18n.t("mail.login") }

  describe '#generate_and_send_otp' do
    it 'generates a 6-digit OTP and sends it to the user' do
      otp = service.generate_and_send_otp

      expect(otp).to match(/\A\d{6}\z/)
      user.reload
      expect(user.otp.to_s).to eq(otp)
      expect(user.otp_generated_at).to be_within(2.seconds).of(Time.current)
      expect(user.otp_resend_count).to eq(1)
    end

    it 'does not generate OTP if request limit exceeded' do
      allow(service).to receive(:can_send_code?).and_return(false)

      expect {
        service.generate_and_send_otp
      }.to raise_error(Errors::TooManyRequestError)
    end

    it 'returns nil for TOTP channel' do
      expect(service.generate_and_send_otp("totp", :login)).to be_nil
    end

    it 'returns nil when channel is totp' do
      expect(service.generate_and_send_otp('totp')).to be_nil
    end

    it 'handles invalid channel gracefully' do
      expect(service.generate_and_send_otp('invalid_channel')).to match(/\A\d{6}\z/)
    end
  end

  describe '#validate_otp' do
    before do
      @otp = service.generate_and_send_otp
    end

    it 'returns true for correct, unexpired OTP' do
      expect(service.validate_otp(@otp)).to be true
    end

    it 'returns false for incorrect OTP' do
      expect(service.validate_otp("000000")).to be false
    end

    it 'returns false for expired OTP' do
      user.update(otp_generated_at: 10.minutes.ago)
      expect(service.validate_otp(@otp)).to be false
    end

    it 'returns false if OTP is nil' do
      user.update(otp: nil)
      expect(service.validate_otp("123456")).to be false
    end

    it 'returns false for non-numeric OTP' do
      expect(service.validate_otp("abcdef")).to be false
    end

    it 'returns false for OTP with invalid length' do
      expect(service.validate_otp("12345")).to be false
      expect(service.validate_otp("1234567")).to be false
    end

    context 'when using TOTP' do
      it 'delegates to verify_with_authenticator' do
        expect(service).to receive(:verify_with_authenticator).with('123456')
        service.validate_otp('123456', 'totp')
      end
    end
  end

  describe '#clear_code!' do
    it 'resets OTP-related attributes on user' do
      service.generate_and_send_otp
      service.clear_code!
      user.reload

      expect(user.otp).to be_nil
      expect(user.otp_generated_at).to be_nil
      expect(user.otp_resend_count).to eq(0)
    end
  end

  describe '#record_code!' do
    it 'sets otp, timestamp and increments resend count' do
      expect {
        service.record_code!('123456')
      }.to change { user.reload.otp_resend_count }.by(1)

      expect(user.otp.to_s).to eq('123456')
      expect(user.otp_generated_at).to be_within(2.seconds).of(Time.current)
    end

    it 'succeeds even if OTP is already nil' do
      user.update(otp: nil, otp_generated_at: nil)
      expect { service.clear_code! }.not_to raise_error
    end
  end

  describe '#send_otp_via_email' do
    it 'calls UserMailer with correct arguments' do
      mailer = double("Mailer", deliver_later: true)
      expect(UserMailer).to receive(:send_otp).with(user, anything, action).and_return(mailer)

      service.send_otp_via_email(action)
    end
  end

  describe '#send_otp_via_channel' do
    it 'routes to email method for "email" channel' do
      expect(service).to receive(:send_otp_via_email).with(action)
      service.send_otp_via_channel("email", action)
    end

    it 'routes to sms method for "sms" channel' do
      expect(service).to receive(:send_otp_via_sms).with(action)
      service.send_otp_via_channel("sms", action)
    end
  end

  describe '#can_send_code?' do
    context 'when time window expired' do
      it 'returns true regardless of resend count' do
        user.update(otp_generated_at: 2.hours.ago, otp_resend_count: 100)
        expect(service.send(:can_send_code?)).to be true
      end
    end

    context 'when within time window and resend count below limit' do
      it 'returns true' do
        user.update(otp_generated_at: Time.current, otp_resend_count: 2)
        expect(service.send(:can_send_code?)).to be true
      end
    end

    context 'when within window and resend count exceeds limit' do
      it 'returns false' do
        user.update(otp_generated_at: Time.current, otp_resend_count: 5)
        expect(service.send(:can_send_code?)).to be false
      end
    end
  end

  describe '#setup_totp' do
    let(:setup_result) { service.setup_totp }
    let(:current_time) { Time.zone.local(2025, 6, 24, 12, 0, 0) }

    it 'generates a random secret' do
      expect(setup_result[:secret_key]).to be_present
      expect(setup_result[:secret_key]).to match(/\A[A-Z2-7]{32}\z/) # Base32 format
    end

    it 'generates a QR code' do
      expect(setup_result[:qr_code]).to be_present
      expect(setup_result[:qr_code]).to match(/\A[A-Za-z0-9+\/]+={0,2}\z/) # Base64 format
    end

    it 'generates a QR code URL' do
      expect(setup_result[:qr_code_url]).to include('otpauth://totp/')
      expect(setup_result[:qr_code_url]).to include(CGI.escape(user.email))
      expect(setup_result[:qr_code_url]).to include(CGI.escape(OtpService::ISSUER))
    end

    it 'stores the secret temporarily in cache' do
      secret = setup_result[:secret_key]
      expect(Rails.cache.read("totp_setup_#{user.id}")).to eq(secret)
    end

    it 'generates unique secrets for different users' do
      another_user = create(:user)
      another_service = described_class.new(another_user)
      another_result = another_service.setup_totp

      expect(setup_result[:secret_key]).not_to eq(another_result[:secret_key])
    end
  end

  describe '#verify_with_authenticator' do
    let(:secret) { ROTP::Base32.random }
    let(:totp) { ROTP::TOTP.new(secret, issuer: OtpService::ISSUER) }
    let(:current_time) { Time.zone.local(2025, 6, 24, 12, 0, 0) }

    context 'when user has TOTP enabled' do
      before do
        user.update!(totp_secret: secret)
      end

      it 'returns a timestamp for valid TOTP code' do
        travel_to current_time do
          valid_code = totp.now
          result = service.verify_with_authenticator(valid_code)
          expect(result).to be_a(Integer)
          expect(result).to be_within(30).of(current_time.to_i)
        end
      end

      it 'returns falsey value for invalid TOTP code' do
        travel_to current_time do
          result = service.verify_with_authenticator('000000')
          expect(result).to be_falsey
        end
      end

      it 'accepts codes within drift window' do
        travel_to current_time do
          valid_code = totp.at(current_time - 15)
          result = service.verify_with_authenticator(valid_code)
          expect(result).to be_a(Integer)
          expect(result).to be_within(30).of(current_time.to_i)
        end
      end

      it 'rejects codes outside drift window' do
        travel_to current_time do
          invalid_code = totp.at(current_time - 60)
          result = service.verify_with_authenticator(invalid_code)
          expect(result).to be_falsey
        end
      end
    end

    context 'when user has no TOTP setup' do
      it 'returns false' do
        expect(service.verify_with_authenticator('123456')).to be false
      end

      it 'returns false for empty code' do
        expect(service.verify_with_authenticator('')).to be false
      end

      it 'returns false for nil code' do
        expect(service.verify_with_authenticator(nil)).to be false
      end
    end
  end

  describe '#verify_totp_setup' do
    let(:otp_code) { '123456' }
    let(:secret) { ROTP::Base32.random }

    before do
      Rails.cache.write("totp_setup_#{user.id}", secret, expires_in: 10.minutes)
    end

    after do
      Rails.cache.clear
    end

    context 'when OTP code is valid' do
      before do
        allow_any_instance_of(ROTP::TOTP).to receive(:verify).with(otp_code, drift_behind: 15).and_return(Time.now.to_i)
      end

      it 'enables TOTP for the user' do
        expect(service.verify_totp_setup(otp_code)).to be true
        user.reload
        expect(user.totp_secret).to eq(secret)
        expect(user.preferred_2fa).to eq("totp")
      end

      it 'clears the temporary secret from cache' do
        service.verify_totp_setup(otp_code)
        expect(Rails.cache.read("totp_setup_#{user.id}")).to be_nil
      end
    end

    context 'when OTP code is invalid' do
      before do
        allow_any_instance_of(ROTP::TOTP).to receive(:verify).with(otp_code, drift_behind: 15).and_return(false)
      end

      it 'raises an error' do
        expect {
          service.verify_totp_setup(otp_code)
        }.to raise_error(Errors::InvalidInput, "Invalid OTP code")
      end

      it 'does not update user settings' do
        expect {
          service.verify_totp_setup(otp_code) rescue nil
        }.not_to change { user.reload.attributes }
      end
    end

    context 'when OTP code is blank' do
      let(:otp_code) { "" }

      it 'raises an error' do
        expect {
          service.verify_totp_setup(otp_code)
        }.to raise_error(Errors::InvalidInput, "Invalid OTP code")
      end
    end

    context 'when setup session has expired' do
      before do
        Rails.cache.clear
      end

      it 'raises an error' do
        expect {
          service.verify_totp_setup(otp_code)
        }.to raise_error(Errors::InvalidInput, '2FA setup session expired. Please start setup again.')
      end
    end
  end
end
