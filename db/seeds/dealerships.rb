# Create Dealership Groups
puts "Creating dealership groups..."

group1 = DealershipGroup.create!(
  name: "AutoNation Australia",
  description: "Australia's largest automotive retail group",
  signup_code: "AUTO2024",
  status: :active,
  contract_end_date: 1.year.from_now
)

group2 = DealershipGroup.create!(
  name: "DriveTime Motors",
  description: "Independent dealership network",
  signup_code: "DRIVE2024",
  status: :active,
  contract_end_date: 2.years.from_now
)

# Create Dealerships
puts "Creating dealerships..."

# Dealerships for AutoNation
dealership1 = Dealership.create!(
  name: "AutoNation Toyota Melbourne",
  long_name: "AutoNation Toyota Melbourne CBD",
  status: :active,
  abn: "***********",
  address_line1: "123 Swanston Street",
  suburb: "Melbourne",
  state: "Victoria",
  postcode: "3000",
  country: :au,
  phone: "+***********",
  email: "<EMAIL>",
  setting_date_format: :dd_mm_yyyy,
  setting_time_zone: :melbourne,
  setting_distance_unit: :kilometers,
  dealership_group: group1
)

# Create settings for dealership1
dealership1.create_dealership_features_setting!(
  advance_booking_enabled: true,
  insurance_waiver_enabled: true,
  dealer_drive_subscription: true,
  appraisals_subscription: true,
  setting_recent_customer_age: 90
)

dealership1.create_dealership_email_setting!(
  send_test_drive_review_email: true,
  send_test_drive_terms_email: true,
  send_loan_terms_email: true,
  loan_review_email_enabled: true,
  send_email_for_bookings: :both,
  level1_odometer_warning_km: 1000,
  level1_odometer_warning_email: "<EMAIL>",
  level2_odometer_warning_km: 2000,
  level2_odometer_warning_email: "<EMAIL>",
  email_from_address: "<EMAIL>",
  email_display_name: "AutoNation Toyota Melbourne"
)

dealership1.create_dealership_terms_setting!(
  insurance_waiver_text: "By proceeding with the test drive, you agree to our insurance terms."
)

dealership2 = Dealership.create!(
  name: "AutoNation Honda Sydney",
  long_name: "AutoNation Honda Sydney Central",
  status: :active,
  abn: "***********",
  address_line1: "456 George Street",
  suburb: "Sydney",
  state: "New South Wales",
  postcode: "2000",
  country: :au,
  phone: "+***********",
  email: "<EMAIL>",
  setting_date_format: :dd_mm_yyyy,
  setting_time_zone: :sydney,
  setting_distance_unit: :kilometers,
  dealership_group: group1
)

# Create settings for dealership2
dealership2.create_dealership_features_setting!(
  advance_booking_enabled: true,
  insurance_waiver_enabled: true,
  dealer_drive_subscription: true,
  appraisals_subscription: true,
  setting_recent_customer_age: 90
)

dealership2.create_dealership_email_setting!(
  send_test_drive_review_email: true,
  send_test_drive_terms_email: true,
  send_loan_terms_email: true,
  loan_review_email_enabled: true,
  send_email_for_bookings: :both,
  level1_odometer_warning_km: 1000,
  level1_odometer_warning_email: "<EMAIL>",
  level2_odometer_warning_km: 2000,
  level2_odometer_warning_email: "<EMAIL>",
  email_from_address: "<EMAIL>",
  email_display_name: "AutoNation Honda Sydney"
)

dealership2.create_dealership_terms_setting!(
  insurance_waiver_text: "By proceeding with the test drive, you agree to our insurance terms."
)

# Dealerships for DriveTime
dealership3 = Dealership.create!(
  name: "DriveTime Perth",
  long_name: "DriveTime Perth CBD",
  status: :active,
  abn: "***********",
  address_line1: "789 Hay Street",
  suburb: "Perth",
  state: "Western Australia",
  postcode: "6000",
  country: :au,
  phone: "+***********",
  email: "<EMAIL>",
  setting_date_format: :dd_mm_yyyy,
  setting_time_zone: :perth,
  setting_distance_unit: :kilometers,
  dealership_group: group2
)

# Create settings for dealership3
dealership3.create_dealership_features_setting!(
  advance_booking_enabled: false,
  insurance_waiver_enabled: false,
  dealer_drive_subscription: false,
  appraisals_subscription: false,
  setting_recent_customer_age: 60
)

dealership3.create_dealership_email_setting!(
  send_test_drive_review_email: false,
  send_test_drive_terms_email: false,
  send_loan_terms_email: false,
  loan_review_email_enabled: false,
  send_email_for_bookings: :none,
  email_from_address: "<EMAIL>",
  email_display_name: "DriveTime Perth"
)

dealership3.create_dealership_terms_setting!

# Create Dealership Alerts
puts "Creating dealership alerts..."

# Alerts for AutoNation Toyota Melbourne
DealershipAlert.create!(
  dealership: dealership1,
  alert_type: :warning,
  threshold: 5,
  emails: "<EMAIL>,<EMAIL>"
)

DealershipAlert.create!(
  dealership: dealership1,
  alert_type: :error,
  threshold: 10,
  emails: "<EMAIL>,<EMAIL>,<EMAIL>"
)

# Alerts for AutoNation Honda Sydney
DealershipAlert.create!(
  dealership: dealership2,
  alert_type: :warning,
  threshold: 5,
  emails: "<EMAIL>,<EMAIL>"
)

# Alerts for DriveTime Perth
DealershipAlert.create!(
  dealership: dealership3,
  alert_type: :info,
  threshold: 3,
  emails: "<EMAIL>"
)

puts "Dealership Seed created successfully!"
