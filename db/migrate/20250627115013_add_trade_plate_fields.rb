class AddTradePlateFields < ActiveRecord::Migration[8.0]
  def change
    create_table :trade_plates do |t|
      t.references :dealership, null: false, foreign_key: true
      t.string :number, null: false
      t.date :expiry
      t.string :uuid, null: false, limit: 36
      t.integer :status, null: false, limit: 3, unsigned: true

      t.timestamps
    end
    add_index :trade_plates, :uuid, unique: true
    add_index :trade_plates, [ :dealership_id, :number ], unique: true
    add_reference :drives, :trade_plate, null: true, foreign_key: true
  end
end
