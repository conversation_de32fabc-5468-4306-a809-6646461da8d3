module Errors
  class AuthenticationError < StandardError; end
  class InternalError < StandardError; end
  class RecordNotFound < StandardError; end
  class InvalidInput < StandardError; end
  class TooManyRequestError < StandardError; end
  class MissingToken < AuthenticationError; end
  class MissingDeviceId < AuthenticationError; end
  class InvalidToken < AuthenticationError; end
  class TokenExpired < AuthenticationError; end
  class InvalidDevice < AuthenticationError; end
  class InvalidSession < AuthenticationError; end
  class UserNotFound < RecordNotFound; end
  class InvalidRefreshToken < AuthenticationError; end
  class RefreshTokenExpired < AuthenticationError; end
end
