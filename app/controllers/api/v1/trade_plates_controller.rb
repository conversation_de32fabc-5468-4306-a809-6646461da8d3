# frozen_string_literal: true

class Api::V1::TradePlatesController < Api::V1::BaseController
  def index
    trade_plates = dealership.trade_plates.includes(:drives)
    render_success_response(
      "Trade plates retrieved successfully",
      trade_plates: trade_plates.map { |trade_plate| Api::V1::TradePlateSerializer.new(trade_plate).serialize }
    )
  end

  private

  def dealership
    @dealership ||= current_user.dealerships.find(uuid: params[:dealership_uuid])
  rescue ActiveRecord::RecordNotFound
    raise Errors::RecordNotFound, "Dealership not found or you don't have access to it"
  end
end
