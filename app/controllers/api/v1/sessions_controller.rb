module Api
  module V1
    class SessionsController < Devise::SessionsController
      include ResponseHandler

      private

      def respond_with(current_user, _opts = {})
        raise Errors::AuthenticationError, "Invalid username or password" unless current_user&.id

        OtpService.new(current_user).generate_and_send_otp(current_user.preferred_2fa)
        temporary_token, expiry_time = Auth::TokenService.new(nil, current_user).generate_temporary_token
        render_success_response("Require 2FA challenge", {
          temporary_token: temporary_token,
          expires_at: expiry_time,
          token_type: "Bearer",
          challenge_type: current_user.preferred_2fa,
          masked_destination: current_user.masked_destination,
          two_factor_methods: current_user.available_2fa_methods
        })
      end

      def respond_with_navigational_error(error_resource, _opts = {})
        raise Errors::InvalidInput, "Login failed: #{error_resource.errors.full_messages.join(', ')}"
      end
    end
  end
end
