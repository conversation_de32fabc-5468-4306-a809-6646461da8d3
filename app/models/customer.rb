class Customer < ApplicationRecord
  include HasUuid

  belongs_to :dealership
  has_many :drives, dependent: :destroy
  has_one :driver_license, as: :holder, dependent: :destroy
  accepts_nested_attributes_for :driver_license

  # Validation
  validates :first_name, :last_name, :email, presence: true
  validates :company_name, length: { maximum: 255, minimum: 3 }, allow_blank: true
  validates :email, format: { with: URI::MailTo::EMAIL_REGEXP }
  validates :phone_number, length: { maximum: 15, minimum: 6 }, presence: true
  validates :external_id, length: { maximum: 50, minimum: 1 }, allow_blank: true

  # Enum
  enum :gender, {
    unspecified: 0,
    male: 1,
    female: 2,
    other: 3
  }, default: :unspecified

  def full_name
    "#{first_name} #{last_name}"
  end
end
