class Vehicle < ApplicationRecord
  include HasUuid


  # Associations
  belongs_to :dealership
  has_many :drives, dependent: :destroy
  has_one :last_damage_report, -> { where(report_type: DamageReport::VEHICLE) }, class_name: "DamageReport", dependent: :destroy
  has_one :last_known_location, as: :trackable, class_name: "GpsLocation", dependent: :destroy
  has_many_attached :photos

  # Validations
  validates :make, :model, :year, presence: true
  validates :rego, length: { maximum: 20 }
  validates :vin, length: { maximum: 17 } # VIN is 17 characters long in Australia
  validates :photos, content_type: [ "image/png", "image/jpeg" ],
                    size: { less_than: 5.megabytes },
                    limit: { max: 5 }
  validates :year, numericality: {
    greater_than: 1900,
    less_than_or_equal_to: -> { Date.current.year + 1 }
  }

  enum :status, {
    available: 0,
    in_use: 1,
    out_of_service: 2,
    deleted: 3,
    sold: 4,
    enquiry: 5
  }, default: :available

  enum :vehicle_type, {
    new_vehicle: 0,
    demo: 1,
    old: 2
  }

  scope :available_for_test_drive, -> { where(status: :available) }

  def display_name
    [ year, make, model ].compact.join(" ")
  end

  def currently_on_test_drive?
    drives.active.exists?
  end
end
